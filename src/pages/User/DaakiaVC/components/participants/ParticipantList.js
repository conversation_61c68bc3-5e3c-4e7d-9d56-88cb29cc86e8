/* eslint-disable no-unused-vars */
import React, { useState, useMemo, useCallback, useEffect, memo } from "react";
import { List, Input, Collapse, Button, Avatar } from "antd";
import { isMobileBrowser } from "@livekit/components-core";
import { FaCaretRight, FaCaretDown  } from "react-icons/fa";
import { ParticipantCard } from "./ParticipantCard";
import "../../styles/index.scss";
// import "./ParticipantList.scss";

import "./styles/ParticipantList.scss";
import SideDrawer from "../SideDrawer";
import { BreakoutRoomModal } from "./BreakoutRoomModal";
import { WaitingParticipantCard } from "./WaitingParticipantCard";
import { APIrequest } from "../../API/axios";
import { Endpoints } from "../../API/Endpoints/routes";
import { DrawerState } from "../../utils/constants";
import { useVideoConferencesContext } from "../../context/VideoConferencesContext";
import { getLocalStorageToken, parseMetadata } from "../../utils/helper";
import { SaasService } from "../../SaaS/services/saasServices";
import { useSaasHelpers } from "../../SaaS/helpers/helpers";

const { Panel } = Collapse;
const { Search } = Input;

const ParticipantList = memo(function ParticipantList({
  remoteParticipants,
  localParticipant,
  showParticipantsList,
  id,
  isHost,
  layoutContext,
  isCoHost,
  lobbyParticipants,
  setRemoteRaisedHands,
  coHostToken,
  breakoutRooms,
  setShowRaiseHand,
  currentRoomName,
  setBreakoutRoomDuration,
  meetingDetails,
  setBreakoutRooms,
  setRoomKeyCounter,
  isBreakoutRoomCnfigSet,
  setIsBreakoutRoomCnfigSet,
  meetingFeatures,
  setselectedprivatechatparticipant,
  setprivatechatparticipants,
  privatechatparticipants,
  setshowprivatechat,
  forcemute,
  forcevideooff,
  room,
  isBreakoutRoom,
  allowLiveCollabWhiteBoard,
  isWhiteboardOpen,
  setToastNotification,
  setToastStatus,
  setShowToast,
  removeParticipantFromLobby,
  clearAllLobbyParticipants,
}) {
  const { setOpenDrawer } = useVideoConferencesContext();
  const [searchTerm, setSearchTerm] = useState("");
  const [isBreakoutRoomModalOpen, setIsBreakoutRoomModalOpen] = useState(false);
  // Local state for lobby participants management
  const [localLobbyParticipants, setLocalLobbyParticipants] = useState(new Map());
  const remote = Array.from(remoteParticipants.values());
  const remoteParticipantsArray = [localParticipant, ...remote];
  const [numberOfCohostCurrent, setNumberOfCohostCurrent] = useState(0);
  const [isInMeetingParticipantsOpen, setIsInMeetingParticipantsOpen] = useState(true);
  const [isOutMeetingParticipantsOpen, setIsOutMeetingParticipantsOpen] = useState(false);
  const [inviteeList, setInviteeList] = useState([]);
  const { isSaaS, saasHostToken } = useSaasHelpers();

  // Count of number co host in the meeting currently
  useEffect(() => {
    let count = 0;
    remoteParticipantsArray.forEach((participant) => {
      if (parseMetadata(participant?.metadata).role_name === "cohost") count += 1;
    });
    // Only update state if count is different (prevent unnecessary renders)
    setNumberOfCohostCurrent((prevCount) =>
      prevCount !== count ? count : prevCount
    );
  }, [numberOfCohostCurrent]);

  // Memoized lobby participants to prevent unnecessary re-renders
  const memoizedLobbyParticipants = useMemo(() => {
    return lobbyParticipants;
  }, [lobbyParticipants]);

  // Filter participants based on the search term
  const filteredParticipants = useMemo(() => {
    if (!searchTerm || searchTerm.trim() === "") return remoteParticipantsArray;
    return remoteParticipantsArray.filter((participant) =>
      participant.name.toLowerCase().includes(searchTerm.toLowerCase().trim())
    );
  }, [searchTerm, remoteParticipantsArray]);

  // On Search Function
  const onSearch = (value) => {
    setSearchTerm(value);
  };

  // Sync local state with memoized lobby participants
  useEffect(() => {
    setLocalLobbyParticipants(new Map(memoizedLobbyParticipants));
  }, [memoizedLobbyParticipants]);

  // Update lobbyParticipants periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTime = Date.now();
      const timeLimit = 12 * 1000; // 12 seconds in milliseconds

      setLocalLobbyParticipants((prev) => {
        const updatedMap = new Map(prev);
        Array.from(updatedMap.keys()).forEach((key) => {
          const participant = updatedMap.get(key);
          if (participant) {
            const timeDifference = currentTime - participant.time;
            if (timeDifference > timeLimit) {
              updatedMap.delete(key);
            }
          }
        });
        return updatedMap;
      });
    }, 2000); // Check every 2 second

    return () => clearInterval(interval); // Clean up the interval on component unmount
  }, [memoizedLobbyParticipants]);

  // Filter lobby participants based on time and map to required fields
  const filteredLobbyParticipants = useMemo(() => {
    const currentTime = Date.now();
    const timeLimit = 12 * 1000; // 12 seconds in milliseconds
    return Array.from(memoizedLobbyParticipants.values())
      .filter((participant) => currentTime - participant.time <= timeLimit)
      .map((participant) => ({
        display_name: participant.display_name,
        request_id: participant.request_id,
      }));
  }, [memoizedLobbyParticipants]);

  // Handle status update for all participants in lobby
  const handleStatus = useCallback(
    async (status) => {
      try {
        const response = await APIrequest({
          method: Endpoints.status_update_participant_lobby.method,
          endpoint: Endpoints.status_update_participant_lobby.url,
          payload: {
            meeting_uid: id,
            is_admit_all: status,
          },
          token: coHostToken,
        });
        if (response.success === 0) {
          setToastNotification("Error updating participant status");
          setToastStatus("error");
          setShowToast(true);
        } else {
          // Clear lobby participants on successful status update
          clearAllLobbyParticipants();
          setLocalLobbyParticipants(new Map());
          setToastNotification(status ? "All participants admitted" : "All participants denied");
          setToastStatus("success");
          setShowToast(true);
        }
      } catch (error) {
        setToastNotification(error.message);
        setToastStatus("error");
        setShowToast(true);
        // console.error("Error updating participant status", error);
      }
    },
    [id, clearAllLobbyParticipants, setLocalLobbyParticipants]
  );

  let activeRooms = [];
  useEffect(() => {
    activeRooms = Object.keys(breakoutRooms).filter(
      (key) =>
        !breakoutRooms[key].isDeleted || !("isDeleted" in breakoutRooms[key])
    );
    if (activeRooms.length > 1) {
      setIsBreakoutRoomCnfigSet(true);
    }
  }, [breakoutRooms]);

  const handleBreakoutRoomButton = () => {
    if (activeRooms.length > 1 || isBreakoutRoomCnfigSet) {
      setOpenDrawer((prev) => prev === DrawerState.BREAKOUTROOM ? DrawerState.NONE : DrawerState.BREAKOUTROOM);
    } else {
      setIsBreakoutRoomModalOpen(true);
    }
  };

  const getInviteeList = async () => {
    try {
      const response = await SaasService.getInviteeList(
        id,
        isHost ? isSaaS ? saasHostToken : getLocalStorageToken() : coHostToken
      );
      if (response.success === 1) {
        setInviteeList(response.data);
        // Filter out participants who are already in the meeting
        const remoteParticipantIdentities = remoteParticipantsArray.map(
          participant => JSON.parse(participant?.metadata)?.custom_metadata?.identifier?.toString()
        );
        // Create a filtered list of invitees who are not already in the meeting
        const notInMeetingParticipants = response.data.filter(invitee => {
          // Check if this invitee's identifier is not in the remote participants list
          return !remoteParticipantIdentities.includes(invitee.identifier.toString());
        });
        setInviteeList(notInMeetingParticipants);
      } else {
        console.log("Error getting invitee list: ", response.message);
      }
    } catch (error) {
      console.error("Error getting invitee list", error);
    }
  }

  useEffect(() => {
    getInviteeList();
  }, []);

  return (
    <SideDrawer
      show={showParticipantsList}
      title={currentRoomName === "Main Room" ? "Participants" : currentRoomName}
      isCoHost={isCoHost}
      isHost={isHost}
      localParticipant={localParticipant}
      setRemoteRaisedHands={setRemoteRaisedHands}
      setShowRaiseHand={setShowRaiseHand}
      count={remoteParticipantsArray.length}
      meetingFeatures={meetingFeatures}
      isWhiteboardOpen={isWhiteboardOpen}
    >
      <div className="pt-container-below">
        <Search
          placeholder="Enter name to search..."
          allowClear
          onSearch={onSearch}
          onChange={(e) => onSearch(e.target.value)}
          style={{ width: 200 }}
          className="pt-search"
        />
        {((isHost || isCoHost) && activeRooms.length > 1) ||
          (isBreakoutRoomCnfigSet && <div>{currentRoomName}</div>)}
        {isHost || isCoHost ? (
          filteredLobbyParticipants.length > 0 ? (
            <div className="pt-collapse">
              <Collapse defaultActiveKey={["1"]} accordion>
                <Panel
                  header={`Waiting in Lobby (${filteredLobbyParticipants.length})`}
                  key="1"
                  className="wil"
                >
                  <div className="waiting-in-lobby-buttons">
                    <Button type="text" onClick={() => handleStatus(false)}>
                      Deny All
                    </Button>
                    <Button type="text" onClick={() => handleStatus(true)}>
                      Allow All
                    </Button>
                  </div>
                  <List
                    dataSource={filteredLobbyParticipants}
                    renderItem={(participant) => (
                      <WaitingParticipantCard
                        participant={participant}
                        id={id}
                        coHostToken={coHostToken}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        removeParticipantFromLobby={removeParticipantFromLobby}
                      />
                    )}
                  />
                </Panel>
                <Panel
                  header={`In Meeting (${filteredParticipants.length})`}
                  key="2"
                  className="inm"
                >
                  <List
                    dataSource={filteredParticipants}
                    renderItem={(participant) => (
                      <ParticipantCard
                        participant={participant}
                        localParticipant={localParticipant}
                        raisedHand={false}
                        id={id}
                        isHost={isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        coHostToken={coHostToken}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setselectedprivatechatparticipant
                        }
                        setprivatechatparticipants={setprivatechatparticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setshowprivatechat}
                        forcemute={forcemute}
                        forcevideooff={forcevideooff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        breakoutRooms={breakoutRooms}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                        numberOfCohostCurrent = {numberOfCohostCurrent}
                        setNumberOfCohostCurrent = {setNumberOfCohostCurrent}
                      />
                    )}
                  />
                </Panel>
              </Collapse>
            </div>
          ) : ( inviteeList.length > 0 ? (
            <div className="in-meeting-participants-container">
              <div className="in-meeting-participants-list">
                <div 
                  className="in-meeting-participants-title"
                  onClick={() => {
                    setIsInMeetingParticipantsOpen(!isInMeetingParticipantsOpen);
                    setIsOutMeetingParticipantsOpen(false);
                  }}
                >
                  {isInMeetingParticipantsOpen ? <FaCaretDown /> : <FaCaretRight />}
                  In Meeting Participants
                </div>
                {isInMeetingParticipantsOpen && (
                  <List
                    className="in-meeting-participants"
                    dataSource={filteredParticipants}
                    renderItem={(participant) => (
                      <ParticipantCard
                        participant={participant}
                        localParticipant={localParticipant}
                        raisedHand={false}
                        id={id}
                        isHost={isHost}
                        layoutContext={layoutContext}
                        isCoHost={isCoHost}
                        coHostToken={coHostToken}
                        meetingFeatures={meetingFeatures}
                        setselectedprivatechatparticipant={
                          setselectedprivatechatparticipant
                        }
                        setprivatechatparticipants={setprivatechatparticipants}
                        privatechatparticipants={privatechatparticipants}
                        setshowprivatechat={setshowprivatechat}
                        forcemute={forcemute}
                        forcevideooff={forcevideooff}
                        room={room}
                        isBreakoutRoom={isBreakoutRoom}
                        breakoutRooms={breakoutRooms}
                        allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                        numberOfCohostCurrent = {numberOfCohostCurrent}
                        setNumberOfCohostCurrent = {setNumberOfCohostCurrent}
                        setToastNotification={setToastNotification}
                        setToastStatus={setToastStatus}
                        setShowToast={setShowToast}
                      />
                    )}
                  />
                )}
              </div>
              <div className="in-meeting-participants-list">
                <div 
                  className="in-meeting-participants-title"
                  onClick={() => {
                    setIsInMeetingParticipantsOpen(false);
                    setIsOutMeetingParticipantsOpen(!isOutMeetingParticipantsOpen);
                    // if (isOutMeetingParticipantsOpen === false) {
                    //   getInviteeList();
                    // }
                  }}
                >
                  {isOutMeetingParticipantsOpen ? <FaCaretDown /> : <FaCaretRight />}
                  Inactive Participants
                </div>
                {isOutMeetingParticipantsOpen && (
                  <List
                    className="out-meeting-participants"
                    dataSource={inviteeList}
                    renderItem={(participant) => (
                      <>
                        <div className="out-meeting-participants-card">
                          <Avatar style={{ backgroundColor: "#fd4563", verticalAlign: "middle" }}>
                            {participant.screen_name.charAt(0)}
                          </Avatar>
                          <div className="out-meeting-participants-card-name">
                            {participant.screen_name}
                          </div>
                        </div>
                      </>
                    )}
                  />
                )}
              </div>
            </div>
          ) : (
            <List
              className="in-meeting-participants"
              dataSource={filteredParticipants}
              renderItem={(participant) => (
                <ParticipantCard
                  participant={participant}
                  localParticipant={localParticipant}
                  raisedHand={false}
                  id={id}
                  isHost={isHost}
                  layoutContext={layoutContext}
                  isCoHost={isCoHost}
                  coHostToken={coHostToken}
                  meetingFeatures={meetingFeatures}
                  setselectedprivatechatparticipant={
                    setselectedprivatechatparticipant
                  }
                  setprivatechatparticipants={setprivatechatparticipants}
                  privatechatparticipants={privatechatparticipants}
                  setshowprivatechat={setshowprivatechat}
                  forcemute={forcemute}
                  forcevideooff={forcevideooff}
                  room={room}
                  isBreakoutRoom={isBreakoutRoom}
                  breakoutRooms={breakoutRooms}
                  allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                  setToastNotification={setToastNotification}
                  setToastStatus={setToastStatus}
                  setShowToast={setShowToast}
                  numberOfCohostCurrent = {numberOfCohostCurrent}
                  setNumberOfCohostCurrent = {setNumberOfCohostCurrent}
                />
              )}
            />
          )
        )
        ) : (
          <List
            dataSource={filteredParticipants}
            renderItem={(participant) => (
              <ParticipantCard
                participant={participant}
                localParticipant={localParticipant}
                raisedHand={false}
                id={id}
                isHost={isHost}
                layoutContext={layoutContext}
                isCoHost={isCoHost}
                coHostToken={coHostToken}
                meetingFeatures={meetingFeatures}
                setselectedprivatechatparticipant={
                  setselectedprivatechatparticipant
                }
                setprivatechatparticipants={setprivatechatparticipants}
                privatechatparticipants={privatechatparticipants}
                setshowprivatechat={setshowprivatechat}
                forcemute={forcemute}
                forcevideooff={forcevideooff}
                room={room}
                isBreakoutRoom={isBreakoutRoom}
                breakoutRooms={breakoutRooms}
                allowLiveCollabWhiteBoard={allowLiveCollabWhiteBoard}
                setToastNotification={setToastNotification}
                setToastStatus={setToastStatus}
                setShowToast={setShowToast}
                numberOfCohostCurrent = {numberOfCohostCurrent}
                setNumberOfCohostCurrent = {setNumberOfCohostCurrent}
              />
            )}
          />
        )}
      </div>
      <BreakoutRoomModal
        isBreakoutRoomModalOpen={isBreakoutRoomModalOpen}
        setIsBreakoutRoomModalOpen={setIsBreakoutRoomModalOpen}
        breakoutRooms={breakoutRooms}
        remoteParticipantsArray={remoteParticipantsArray}
        meetingDetails={meetingDetails}
        setBreakoutRoomDuration={setBreakoutRoomDuration}
        setBreakoutRooms={setBreakoutRooms}
        setRoomKeyCounter={setRoomKeyCounter}
        setIsBreakoutRoomCnfigSet={setIsBreakoutRoomCnfigSet}
        coHostToken={coHostToken}
        localParticipant={localParticipant}
        setToastNotification={setToastNotification}
        setToastStatus={setToastStatus}
        setShowToast={setShowToast}
      />
      {!isMobileBrowser() &&
        meetingFeatures?.breakout_room === 1 &&
        (isHost || isCoHost) && (
          <div className="br-create-room">
            <Button
              type="primary"
              shape="round"
              onClick={() => {
                handleBreakoutRoomButton();
              }}
            >
              {activeRooms.length > 1 || isBreakoutRoomCnfigSet
                ? "Go to Breakout Rooms"
                : "Create Breakout Rooms"}
            </Button>
          </div>
        )}
    </SideDrawer>
  );
});

export { ParticipantList };

import "@livekit/components-styles";
import React, { useEffect, useState } from "react";
import moment from "moment";
import { useParams } from "react-router-dom";

import { LiveKitRoom } from "@livekit/components-react";
import { Room } from "livekit-client";
import { defaultUserChoices } from "@livekit/components-core";
import isElectron from "is-electron";
import { Prejoin } from "./customFabs/PreJoin";
import { VideoConference } from "./customFabs/VideoConference";

import { Loader } from "./components/Loader";
import { SettingsControlButton } from "./components/settings/SettingsControlButton";

import { PrejoinService } from "./services/PrejoinServices";
import { BreakoutRoomLoader } from "./components/BreakoutRoomLoader";
import TitleBar from "./components/titleBar";
import LoadingIcon from "./customFabs/icons/BreakoutAnimation2.json";

import {
  decoder,
  getLocalStorage,
  getMediaPermissions,
  setLocalStorage,
} from "./utils/helper";
import { constants, virtualBackground } from "./utils/constants";
import { ReactComponent as PlusIcon } from "./components/settings/icons/Plus.svg";

// Context imports
import { IndexProvider } from "./context/indexContext";
import { VideoConferencesProvider } from "./context/VideoConferencesContext";
import { ParticipantProvider } from "./context/ParticipantContext";
import { ChatProvider } from "./context/ChatContext";
import { SaasProvider } from "./context/SaasConfigContext";

function DaakiaVC({ saasConfig = null }) {
  const maxHeight = Math.min(
    window.screen.height * window.devicePixelRatio,
    1620
  );
  const maxWidth = Math.min(
    window.screen.width * window.devicePixelRatio,
    2880
  );

  const [isWebinarMode, setIsWebinarMode] = useState(false);
  const [serverDetails, setServerDetails] = useState({});
  const [preJoinShow, setPreJoinShow] = useState(true);
  const [isHost, setIsHost] = useState(false);
  const [decodedId, setDecodedId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isPasswordProtected, setIsPasswordProtected] = useState(false);
  const [isInvalidMeeting, setIsInvalidMeeting] = useState(false);
  const [meetingDetails, setMeetingDetails] = useState({});
  const [clientPreferedServerId, setClientPreferedServerId] = useState("ap1"); // eslint-disable-line no-unused-vars
  const [userChoices, setUserChoices] = useState({});
  const [isLobbyMode, setIsLobbyMode] = useState(false);
  const [isMeetingFinished, setIsMeetingFinished] = useState(false);
  const [isMeetingCancelled, setIsMeetingCancelled] = useState(false);
  const [isMeetingRescheduled, setIsMeetingRescheduled] = useState(false);
  const [isUsingBreakoutRoom, setIsUsingBreakoutRoom] = useState(false);
  const [isMovingToRoom, setIsMovingToRoom] = useState(false);
  const [movingRoomToken, setMovingRoomToken] = useState(null);
  const [meetingFeatures, setMeetingFeatures] = useState({});
  const [meetingUserChoices, setMeetingUserChoices] = useState({});
  const [screenShareSources, setScreenShareSources] = useState([]);
  const [isPipWindow, setIsPipWindow] = useState(false);
  const [isSelfVideoMirrored, setIsSelfVideoMirrored] = React.useState(true);
  const isElectronApp = isElectron();
  const [deviceIdAudio, setDeviceIdAudio] = useState("");
  const [deviceIdVideo, setDeviceIdVideo] = useState("");
  const [speakerDeviceId, setSpeakerDeviceId] = useState("");
  const [brightness, setBrightness] = useState(100);
  const [outputVolume, setOutputVolume] = useState(100);
  const [autoVideoOff, setAutoVideoOff] = useState(false);
  const [autoAudioOff, setAutoAudioOff] = useState(false);
  const [room, setRoom] = useState(
    new Room({
      dynacast: true,
      adaptiveStream: true,
      audioCaptureDefaults: {
        echoCancellation: true,
        noiseSuppression: true,
      },
      publishDefaults: {
        screenShareEncoding: {
          maxBitrate: 1_000_000,
          maxFramerate: 5,
        },
        screenShareSimulcastLayers: [
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 800_000,
              maxFramerate: 1,
            },
          },
          {
            width: maxWidth,
            height: maxHeight,
            encoding: {
              maxBitrate: 400_000,
              maxFramerate: 1,
            },
          },
        ],
      },
    })
  );
  const [isRoomFull, setIsRoomFull] = useState(false);


  const handleBrightnessChange = (newBrightness) => {
    setBrightness(newBrightness);
  };

  // Volume change handler (no RPC needed, local only)
  const handleOutputVolumeChange = (newVolume) => {
    setOutputVolume(newVolume);
  };

  // Auto video off change handler (no RPC needed, local only)
  const handleAutoVideoOffChange = (newAutoVideoOff) => {
    setAutoVideoOff(newAutoVideoOff);
  };

  // Auto audio off change handler (no RPC needed, local only)
  const handleAutoAudioOffChange = (newAutoAudioOff) => {
    setAutoAudioOff(newAutoAudioOff);
  };

  useEffect(() => {
    room.prepareConnection("https://ap1sfu-prod.daakia.co.in");
  }, []);

  useEffect(() => {
    const item = localStorage.getItem("lk-user-choices");
    if (!item) {
      setUserChoices(defaultUserChoices);
    }
    setUserChoices(JSON.parse(item));
  }, []);

  const { id } = useParams();

  useEffect(() => {
    if (id && !saasConfig) {
      setDecodedId(() => decoder(id));
    } else if (saasConfig && saasConfig?.meetingId) {
      setDecodedId(() => saasConfig?.meetingId);
    }
  }, []);

  // Fetch for screen share sources in desktop app
  useEffect(() => {
    const getSourcesOrError = async () => {
      try {
        const sourcesOrError = await window.electronAPI.ipcRenderer.invoke(
          "get-screenshare-sources"
        );
        if (sourcesOrError.error) {
          console.error("Error getting sources:", sourcesOrError.message);
          // Handle error (e.g., show notification, update state)
        } else {
          setScreenShareSources(sourcesOrError);
        }
      } catch (error) {
        console.error(
          "Unexpected error while fetching screen share sources:",
          error
        );
        // Handle unexpected errors
      }
    };

    if (isElectron()) {
      getSourcesOrError();
      getMediaPermissions();
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      if (!decodedId) return;
      try {
        const response = await PrejoinService.getMeetingDetails(
          decodedId,
          saasConfig && saasConfig?.hostToken ? saasConfig?.hostToken : null
        );

        if (response.success === 0) {
          setIsInvalidMeeting(() => true);
          return;
        }
        setIsHost(() => response?.data?.is_host);
        const meetingData = response.data;
        const endDate = moment(meetingData?.end_date);
        const currentDate = moment();

        if (
          meetingData?.conference_status_id === 3 &&
          currentDate.isAfter(endDate)
        ) {
          setIsMeetingFinished(true);
          return;
        } else if (meetingData?.conference_status_id === 4) {
          setIsMeetingCancelled(true);
          return;
        } else if (meetingData?.conference_status_id === 5) {
          setIsMeetingRescheduled(true);
          return;
        }

        setIsPasswordProtected(() => response?.data?.is_password);
        setMeetingDetails(() => response?.data);
        setIsWebinarMode(
          () => response?.data?.event_type.toLowerCase() === "webinar"
        );
        setIsLobbyMode(() => response?.data?.is_lobby_mode);
        setLocalStorage(constants.MEETING_DETAILS, response?.data);
        if (saasConfig && saasConfig?.isSaaS === true && saasConfig?.meetingFeatures) {
          setMeetingFeatures(() => saasConfig?.meetingFeatures);
        } else {
          const featureResponse = await PrejoinService.getMeetingFeatures(
            response?.data?.host_subscription_id || 2
          );
          if (featureResponse.success === 1) {
            setMeetingFeatures(() => featureResponse?.data);
          }
          if (
            response?.data?.meeting_logs?.session_participants >=
            Number(featureResponse?.data?.audio_video_conference)
          ) {
            setIsRoomFull(true);
          }
        }
      } catch (error) {
        console.error("Error getting meeting details:", error);
        setIsInvalidMeeting(() => true);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [decodedId]);

  useEffect(() => {
    if (!room || !isMovingToRoom || !movingRoomToken) return;
    room.disconnect();
    setServerDetails({ ...serverDetails, token: null });
    setRoom(
      new Room({
        dynacast: true,
        adaptiveStream: true,
        audioCaptureDefaults: {
          echoCancellation: true,
          noiseSuppression: true,
        },
        publishDefaults: {
          screenShareEncoding: {
            maxBitrate: 1_000_000,
            maxFramerate: 5,
          },
          screenShareSimulcastLayers: [
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 800_000,
                maxFramerate: 1,
              },
            },
            {
              width: maxWidth,
              height: maxHeight,
              encoding: {
                maxBitrate: 400_000,
                maxFramerate: 1,
              },
            },
          ],
        },
      })
    );
    setServerDetails({ ...serverDetails, token: movingRoomToken });
    setMovingRoomToken(null);
    setTimeout(() => {
      setIsMovingToRoom(false);
    }, 3000);
  }, [isMovingToRoom, movingRoomToken]);

  useEffect(() => {
    if (isUsingBreakoutRoom) {
      const meetingChoice = getLocalStorage(constants.MEETING_USER_CHOICES);
      if (meetingChoice) {
        setMeetingUserChoices(meetingChoice);
      }
    }
  }, [isUsingBreakoutRoom]);

  const [backgrounds, setBackgrounds] = useState([
    {
      heading: "Custom",
      effects: [
        {
          label: "Upload",
          icon: <PlusIcon />,
          value: "Upload",
        },
      ],
    },
    ...virtualBackground,
  ]);

  return (
    <IndexProvider>
      <SaasProvider saasConfig={saasConfig}>
        {isLoading ? (
          <div className="loader-container">
            <Loader
              heading="Please wait for a moment"
              description="We are fetching the meeting details for you."
              isLoading
            />
          </div>
        ) : isRoomFull ? (
          <div className="loader-container">
            <Loader
              heading="Room is Full!"
              description={
                isWebinarMode
                  ? "The slot for this webinar is full. Please try after some time"
                  : "Please contact the host for more information."
              }
              isLoading={false}
            />
          </div>
        ) : isInvalidMeeting ? (
          <div className="loader-container">
            <Loader
              heading="No Meeting Found!"
              description="Please check the meeting link and try again."
              isLoading={false}
            />
          </div>
        ) : isMeetingFinished ? (
          <div className="loader-container">
            <Loader
              heading="Meeting has been Ended!"
              description={
                isHost
                  ? "You can start a new meeting."
                  : "Please contact the host for more information."
              }
              isLoading={false}
            />
          </div>
        ) : isMeetingCancelled ? (
          <div className="loader-container">
            <Loader
              heading="Meeting has been Cancelled!"
              description="Please contact the host for more information."
              isLoading={false}
            />
          </div>
        ) : isMeetingRescheduled ? (
          <div className="loader-container">
            <Loader
              heading="Meeting has been Rescheduled!"
              description="Please contact the host for more information."
              isLoading={false}
            />
          </div>
        ) : isMovingToRoom ? (
          <div className="loader-container">
            <BreakoutRoomLoader
              heading="Room Hop!"
              description="Just a sec! We're moving you to your next adventure."
              isLoading
              icon={LoadingIcon}
              rootClass="breakout-room-loader"
            />
          </div>
        ) : preJoinShow ? (
          <Prejoin
            setServerDetails={setServerDetails}
            id={decodedId}
            setPreJoinShow={setPreJoinShow}
            // setIsHost={setIsHost}
            isHost={isHost}
            isPasswordProtected={isPasswordProtected}
            meetingDetails={meetingDetails}
            setClientPreferedServerId={setClientPreferedServerId}
            userChoices={userChoices}
            setUserChoices={setUserChoices}
            isLobbyMode={isLobbyMode}
            isWebinarMode={isWebinarMode}
            setIsPipWindow={setIsPipWindow}
            isPipWindow={isPipWindow}
            room={room}
            backgrounds={backgrounds}
            setBackgrounds={setBackgrounds}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            deviceIdAudio={deviceIdAudio}
            setDeviceIdAudio={setDeviceIdAudio}
            deviceIdVideo={deviceIdVideo}
            setDeviceIdVideo={setDeviceIdVideo}
            brightness={brightness}
            onBrightnessChange={handleBrightnessChange}
            outputVolume={outputVolume}
            onOutputVolumeChange={handleOutputVolumeChange}
            autoVideoOff={autoVideoOff}
            onAutoVideoOffChange={handleAutoVideoOffChange}
            autoAudioOff={autoAudioOff}
            onAutoAudioOffChange={handleAutoAudioOffChange}
            speakerDeviceId={speakerDeviceId}
            setSpeakerDeviceId={setSpeakerDeviceId}
          />
        ) : (
          !preJoinShow &&
          serverDetails.token &&
          serverDetails.serverUrl && (
            <LiveKitRoom
              room={room}
              video={
                isUsingBreakoutRoom
                  ? meetingUserChoices.video
                  : isWebinarMode && !isHost
                  ? false
                  : (() => {
                      // Apply auto video off logic when joining the meeting
                      const finalVideoEnabled = userChoices.autoVideoOff
                        ? false
                        : userChoices.videoEnabled;
                      return finalVideoEnabled
                        ? { deviceId: userChoices.videoDeviceId }
                        : false;
                    })()
              }
              audio={
                isUsingBreakoutRoom
                  ? meetingUserChoices.audio
                  : isWebinarMode && !isHost
                  ? false
                  : (() => {
                      // Apply auto audio off logic when joining the meeting
                      const finalAudioEnabled = userChoices.autoAudioOff
                        ? false
                        : userChoices.audioEnabled;
                      return finalAudioEnabled
                        ? { deviceId: userChoices.audioDeviceId }
                        : false;
                    })()
              }
              token={serverDetails.token}
              serverUrl={serverDetails.serverUrl}
              data-lk-theme="default"
              // style={{ height: "100vh" }}
            >
              {isElectronApp && (
                <TitleBar
                  setIsPipWindow={setIsPipWindow}
                  isPipWindow={isPipWindow}
                  title={meetingDetails?.event_name}
                />
              )}

              <VideoConferencesProvider>
                <ParticipantProvider>
                  <ChatProvider>
                    <VideoConference
                      room={room}
                      SettingsComponent={SettingsControlButton}
                      maxHeight={maxHeight}
                      maxWidth={maxWidth}
                      id={decodedId}
                      isHost={isHost}
                      meetingDetails={meetingDetails}
                      clientPreferedServerId={clientPreferedServerId}
                      isMeetingFinished={isMeetingFinished}
                      setIsMeetingFinished={setIsMeetingFinished}
                      setIsMovingToRoom={setIsMovingToRoom}
                      setMovingRoomToken={setMovingRoomToken}
                      isMovingToRoom={isMovingToRoom}
                      meetingFeatures={meetingFeatures}
                      isWebinarMode={isWebinarMode}
                      setIsUsingBreakoutRoom={setIsUsingBreakoutRoom}
                      token={serverDetails.token}
                      isElectronApp={isElectronApp}
                      screenShareSources={screenShareSources}
                      isPipWindow={isPipWindow}
                      isSelfVideoMirrored={isSelfVideoMirrored}
                      setIsSelfVideoMirrored={setIsSelfVideoMirrored}
                      backgrounds={backgrounds}
                      brightness={brightness}
                      onBrightnessChange={handleBrightnessChange}
                      outputVolume={outputVolume}
                      onOutputVolumeChange={handleOutputVolumeChange}
                      autoVideoOff={autoVideoOff}
                      onAutoVideoOffChange={handleAutoVideoOffChange}
                      autoAudioOff={autoAudioOff}
                      onAutoAudioOffChange={handleAutoAudioOffChange}
                      setBackgrounds={setBackgrounds}
                      deviceIdAudio={deviceIdAudio}
                      setDeviceIdAudio={setDeviceIdAudio}
                      deviceIdVideo={deviceIdVideo}
                      setDeviceIdVideo={setDeviceIdVideo}
                      speakerDeviceId={speakerDeviceId}
                      setSpeakerDeviceId={setSpeakerDeviceId}
                    />
                  </ChatProvider>
                </ParticipantProvider>
              </VideoConferencesProvider>
            </LiveKitRoom>
          )
        )}
      </SaasProvider>
    </IndexProvider>
  );
}

export default DaakiaVC;

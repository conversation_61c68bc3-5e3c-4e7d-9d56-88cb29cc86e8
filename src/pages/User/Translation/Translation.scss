$color-primary: #3b60e4;
$color-secondary: #4a4a4a;
$color-tertiary: #434343;
$active-card-color: #e9eeff;
$card-subtitle-color: #979797;
$card-border-color: #cccccc;
$required-color: #e14045;
$body-border-color: #c5c5c5;
$document-upload-border-color: #9e9e9e;

.translate {
  // background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  &-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    margin-top: 30px;

    &-title {
      font-size: 38px;
      color: $color-primary;
      @media screen and (max-width: 766px) {
        font-size: 30px;
        text-align: center;
      }
    }
    &-subtitle {
      font-size: 25px;
      color: $color-secondary;
      @media screen and (max-width: 766px) {
        font-size: 20px;
        text-align: center;
      }
    }
    &-description {
      font-size: 16px;
      color: $color-tertiary;
      width: 600px;
      text-align: center;
      @media screen and (max-width: 766px) {
        width: 100%;
        font-size: 14px;
        text-align: center;
      }
    }
  }
  &-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1.5rem;
    // padding: 1.5rem;
    max-width: 1200px;
    // margin: 0 auto;
    margin-top: 15px;
  }
  &-card {
    background-color: white;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid $card-border-color;
    padding: 0.4rem 0.7rem;
    border-radius: 10px;
    width: 250px;
    cursor: pointer;
    &:hover,
    &.active {
      background-color: #e9eeff;
    }
    &-icon {
      width: 46px;
      height: 46px;
    }
    &-content {
      display: flex;
      flex-direction: column;
    }
    &-title {
      font-size: 18px;
      color: $color-secondary;
    }
    &-subtitle {
      font-size: 14px;
      color: $card-subtitle-color;
      margin: 0;
    }
  }
  &-body {
    display: flex;
    justify-content: center;
    margin: 2rem 5rem;
    border: 1px solid $body-border-color;
    flex-direction: column;
    border-radius: 12px;
    width: -webkit-fill-available;
    background-color: white;

    @media screen and (max-width: 1024px) {
      margin: 2rem 2rem;
    }

    &-header {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.2rem;
      position: relative;

      // @media screen and (max-width: 768px) {
      //   padding: 1rem;
      //   // flex-direction: column;
      //   align-items: center;
      //   gap: 1rem;
      // }
    }

    &-header-source {
      color: $color-secondary;
      display: flex;
      width: calc(50% - 1rem);
      justify-content: center;
      font-weight: bold;
      align-items: center;
      span {
        color: $required-color;
        font-size: 12px;
        margin-right: 0.3rem;
      }
      &-language-name {
        // font-size: 14px;
        color: $color-primary;
        margin: 0 0.3rem;
        margin-left: 0.5rem;
      }
    }

    &-target-language {
      grid-area: target-language;
      color: $color-secondary;
      display: flex;
      width: calc(50% - 1rem);
      justify-content: center;
      font-weight: bold;
      align-items: center;
      padding: 1rem 2rem;

      span {
        color: $required-color;
        font-size: 12px;
        margin-right: 0.3rem;
      }

      @media screen and (max-width: 768px) {
        padding: 1rem;
        justify-content: center;
        width: 100%;
      }
    }

    &-header-arrowdown {
      display: flex;
      margin: 7px;
      height: 8px;
      margin-left: 15px;
    }

    &-header-switch {
      grid-area: switch-icon;
      display: flex;
      align-items: center;
      justify-content: center;

      @media screen and (max-width: 768px) {
        transform: rotate(90deg);
        margin: 0.5rem 0;
      }
    }

    &-content {
      grid-area: content-area;
      display: flex;
      height: 22rem;
      border-top: 1px solid $body-border-color;

      @media screen and (max-width: 768px) {
        flex-direction: column;
        height: auto;
      }

      &-input,
      &-output {
        width: 50%;

        @media screen and (max-width: 768px) {
          width: 100%;
          min-height: 250px;
          max-height: 400px;
        }
      }
      &-input {
        display: flex;
        flex-direction: column;
        align-items: center;
        border-right: 1px solid $body-border-color;
        padding: 0.7rem;

        @media screen and (max-width: 768px) {
          border-right: none;
          border-bottom: 1px solid $body-border-color;
        }

        textarea {
          font-size: 16px;
          width: 100%;
          border: none;
          resize: none;
          height: -webkit-fill-available;
          border-bottom: 1px solid $body-border-color;
        }
        &-document {
          height: -webkit-fill-available;
          border-bottom: 1px solid $body-border-color;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow-y: auto;
          &.active {
            align-items: flex-start !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
            gap: 1rem;
            > span {
              width: 100%;
              margin-bottom: 1rem;
              .ant-upload {
                padding: 0.3rem 0;
                .ant-upload-drag-icon {
                  margin-bottom: 0.5rem;
                }
              }
            }
          }
          > span {
            display: flex;
            justify-content: center;
          }
          &-box {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            width: 75% !important;
            flex-direction: column;
            gap: 0.7rem;

            @media screen and (max-width: 768px) {
              width: 90%;
            }

            .document-upload-text {
              display: flex;
              align-items: center;
              gap: 0.5rem;
            }

            .browse-link {
              color: $color-primary;
              cursor: pointer;
            }

            .hidden-file-input {
              display: none;
            }
          }
          &-icon {
            width: 40px;
            height: auto;
          }
        }
      }
      &-output {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 0.7rem;
        &-text {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 16px;
          color: $color-secondary;
          height: 81%;
          max-height: -webkit-fill-available;
          width: 100%;
          overflow-y: auto;
          border-bottom: 1px solid $body-border-color;
          &.active {
            align-items: flex-start !important;
          }
          @media screen and (max-width: 768px) {
            font-size: 14px;
            align-items: flex-start;
            justify-content: flex-start;
            padding: 0.5rem 0.2rem;
            height: -webkit-fill-available;
            min-height: 250px;
            max-height: 400px;
            resize: none;
          }
        }
        &-active {
          justify-content: flex-start;
          align-items: flex-start;
          padding: 1rem;
        }
        &-action {
          width: 100%;
          height: 15%;
          display: flex;
          justify-content: flex-end;
          align-items: center;
          svg {
            font-size: 20px;
            color: $color-primary;
            cursor: pointer;
            margin-right: 1rem;
            @media (max-width: 600px) {
              font-size: 1em;
            }
          }
        }
      }
    }
  }
  &-button {
    background-color: $color-primary;
    border: none;
    font-size: 20px;
    height: 40px;
    width: 150px;
    border-radius: 6px;
    margin-top: 0.3rem;
    margin-bottom: 0.3rem;
  }
}

// language dropdown
$language-dropdown-background-color: #efefef;
.language-dropdown {
  position: absolute;
  top: 63px;
  left: 0;
  background-color: $language-dropdown-background-color;
  width: 100%;
  padding: 0.7rem;
  z-index: 1000;
  border-radius:12px;

  &-input {
    border-radius: 8px;
    height: 37px;
  }
  &-list {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    max-height: 272px;
    overflow-y: auto;
    margin-top: 1rem;
    background-color: #fff;
    border-radius: 8px;
    padding: 1rem;

    @media screen and (max-width: 1028px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media screen and (max-width: 800px) {
      grid-template-columns: repeat(3, 1fr);
      padding: 0.7rem;
    }

    @media screen and (max-width: 600px) {
      grid-template-columns: repeat(2, 1fr);
      padding: 0.5rem;
    }

    @media screen and (max-width: 480px) {
      grid-template-columns: repeat(1, 1fr);
    }
    &-item {
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 8px;
      &:hover {
        background-color: #e9eeff;
      }
    }
  }
}

$selected-files-border-color: #d4d4d4;
.selected-files {
  width: 100%;
  padding: 10px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;

  h4 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #333;
  }

  &-list {
    list-style: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin: 0;
    &-item {
      display: grid;
      grid-template-columns: 40px 1fr 40px;
      align-items: center;
      gap: 0.5rem;
      justify-content: space-between;
      border: 1px solid $selected-files-border-color;
      padding: 0.5rem;
      border-radius: 8px;
      &-key{
        display: grid;
        grid-template-columns: 40px 1fr 20px 40px;
        align-items: center;
        gap: 0.5rem;
        justify-content: space-between;
        border: 1px solid $selected-files-border-color;
        padding: 0.5rem;
        border-radius: 8px;
      }
    }
    &-icon {
      width: 40px;
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background-color: #e9eeff;
    }
    &-remove {
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #e9eeff;
    }
    .file-name {
      flex: 1 ;
      margin-right: 10px;
      word-break: break-all;
      font-weight: 500;
      font-size: 14px;
    }
    .file-name-translated{
      // flex: 1 !important;
      margin-right: 10px;
      word-break: break-all;
      font-weight: 500;
      font-size: 14px;
    }
  }

  // li {
  //   padding: 8px;
  //   background: white;
  //   border-radius: 4px;
  //   border: 1px solid #e8e8e8;

  //   .file-name {
  //     flex: 1;
  //     margin-right: 10px;
  //     word-break: break-all;
  //   }

  //   .file-size {
  //     color: #666;
  //     margin-right: 10px;
  //     white-space: nowrap;
  //   }
  // }
}

.translation-status {
  flex-direction: column;
  align-items: center;
  // border-right: 1px solid #c5c5c5;
  padding: 0.7rem;
  
  .list{
    align-items: flex-start !important;
    flex-direction: column !important;
    justify-content: flex-start !important;
    gap: 1rem;
  }

}

.translate-history {
  .date-range {
    width: 260px;
    padding-right: 20px;
    border-color: #DEDEDE;
    position: relative;
    border-radius: 8px;
    .ant-picker-suffix {
      left: calc(100% - 20px);
      top: 50%;
      transform: translateY(-50%);
    .ant-picker-range-separator{
        width: 5px;
        height: 5px;
      }
    }
  }
}

.character-count {
  display: flex;
  padding: 1rem;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  &-box {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    svg {
      font-size: 20px;
      color: $color-primary;
    }
  }
}
.translate-body-content-input,
.translate-body-content-output,
.selected-files-list,
.selected-files-list-key {
  &::-webkit-scrollbar {
    width: thin;
    background: transparent;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background: #D9D9D9;
    border-radius: 8px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background: #a0a0a0;
  }
  // Firefox
  scrollbar-width: thin;
  scrollbar-color: #c5c5c5 #f0f0f0;
}


import React, { useState, useEffect, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import "./Translation.scss";
import { IoLanguageOutline } from "react-icons/io5";
import TransalteCard from "./Components/TransalteCard";
import { ReactComponent as World } from "./Assets/world.svg";
import { ReactComponent as Document } from "./Assets/document.svg";
import { ReactComponent as History } from "./Assets/history.svg";
import { ReactComponent as Audio } from "./Assets/audio.svg";
import TranslateHeader from "./Components/TranslateHeader";
import TranslateBody from "./Components/TranslateBody";
import TranslateAudio from "./Components/TranslateAudio";
import { TranslateServices } from "../../../services";
import {
  getLanguageList,
  // getUserSubscription,
  selectLanguageData,
  // selectProfileData,
  selectSubscriptionData,
  selectUserAccountData,
  // selectProfileData,
} from "../../../redux/UserSlice/index.slice";
import {
  checkValidCount,
} from "../../../components";
import {
  getCurrentActiveSubscription,
  modalNotification,
  getIscorporateActive,
  getActiveAccount
} from "../../../utils";
import TranslateHistory from "./Components/TranslateHistory";

export default function Translation() {
  const [selectedCard, setSelectedCard] = useState("text");
  const [translateText, setTranslateText] = useState({
    source: "",
    target: "",
    text: "",
    translatedText: "",
  });
  const [isButtonDisabled, setIsButtonDisabled] = useState(true);
  const [documentTranslationLanguageList, setDocumentTranslationLanguageList] = useState([]);
  const [translationStatus, setTranslationStatus] = useState(null);
  const [isTranslationLoading, setIsTranslationLoading] = useState(false);
  const [audioTranslationLanguageList, setAudioTranslationLanguageList] = useState([]);
  const [fromLangDropdown, setFromLangDropdown] = useState(false);
  const [toLangDropdown, setToLangDropdown] = useState(false);
  const [search, setSearch] = useState("");
  const dispatch = useDispatch();
  const languageData = useSelector(selectLanguageData);
  const userActiveSubscription = useSelector(selectSubscriptionData);
  const account = useSelector(selectUserAccountData);
  // const userProfileData = useSelector(selectProfileData);
  const [translatedCount, setTranslatedCount] = useState(0);
  const [remainingCount, setRemainingCount] = useState(0);
  const [currentSubscription, setCurrentSubscription] = useState(null);
  const [translatedDocuments, setTranslatedDocuments] = useState([]);
  const [isSourceAutoDetected, setIsSourceAutoDetected] = useState(false);
  const [resetKey, setResetKey] = useState(0);

  // Calculate current subscription and character counts
  useEffect(() => {
    if (userActiveSubscription?.length > 0) {
      const subscriptionData = getCurrentActiveSubscription(
        userActiveSubscription,
        "translation",
        true
      );
      setCurrentSubscription(subscriptionData);
      const total = subscriptionData?.Subscription?.SubscriptionFeature?.translation || 0;
      // For now, assume translatedCount is fetched from API or set elsewhere
      setRemainingCount(Math.max(0, checkValidCount(total) - checkValidCount(translatedCount)));
    }
  }, [userActiveSubscription]);

  useEffect(() => {
    if (currentSubscription && account) {
      const isCorporate = getIscorporateActive(account);
      const activeAcc = getActiveAccount(account);
      let bodyData = {
        order_id: currentSubscription?.order_id,
        total_count: 0,
        is_corporate: isCorporate ? 1 : 0,
        corporate_id: activeAcc?.id,
      };
      TranslateServices.getTranslateRemainingCountService(bodyData)
        .then((res) => {
          const { success, data } = res;
          if (success === 1) {
            setTranslatedCount(data?.translation);
            // Update remainingCount as well
            const total = currentSubscription?.Subscription?.SubscriptionFeature?.translation || 0;
            setRemainingCount(Math.max(0, checkValidCount(total) - checkValidCount(data?.translation)));
          }
        })
        .catch((error) => {
          // Optionally handle error
          console.log("error", error);
        });
    }
  }, [currentSubscription, account]);

  // Modal notification if quota exceeded
  const errorMsgCount0 = () => {
    return modalNotification({
      type: "error",
      message:
        "You have utilized the total characters as per plan. Please upgrade to continue using the translation services",
    });
  };

  // document translation language list
  const getDocumentTranslationLanguageList = async () => {
    try {
      const response =
        await TranslateServices.documentTranslationLanguageListService();
      const { data } = response;
      setDocumentTranslationLanguageList(data?.languages);
    } catch (error) {
      modalNotification({
        type: "error",
        message: "Something went wrong",
      });
    }
  };

  // get audio language list
  const getAudioLanguageList = async () => {
    try {
      const response = await TranslateServices.getLanguageListService();
      const { success, data } = response;
      if (success === true) {
        setAudioTranslationLanguageList(
          data.map((item) => ({
            label: item[0].toUpperCase() + item.slice(1),
            value: item,
          }))
        );
      } else {
        modalNotification({
          type: "error",
          message: "Something went wrong",
        });
      }
    } catch (error) {
      console.error("error", error);
    }
  };

  // from language dropdown open
  const sourceLanguageDropdownOpen = () => {
    setFromLangDropdown(!fromLangDropdown);
    setSearch("");
    setIsSourceAutoDetected(false);
  };

  // from language dropdown close
  const sourceLanguageDropdownClose = () => {
    setFromLangDropdown(false);
    setSearch("");
  };

  const targetLanguageDropdownOpen = () => {
    setToLangDropdown(!toLangDropdown);
    setSearch("");
  };

  // target language dropdown close
  const targetLanguageDropdownClose = () => {
    setToLangDropdown(false);
    setSearch("");
  };

  // Helper function to check if translation type is available in subscription
  const isTranslationTypeAvailable = (translationType) => {
    if (!currentSubscription?.Subscription?.SubscriptionTranslationTypes) {
      return false;
    }
    return currentSubscription.Subscription.SubscriptionTranslationTypes.some(
      (type) => type.translation_type === translationType
    );
  };

  // card data for translate cards
  const getAllCardData = () => [
    {
      id: "text",
      title: "Translate text",
      des: `${languageData?.length} languages`,
      image: World,
      translationType: "plainText",
    },
    {
      id: "document",
      title: "Translate document",
      des: `${documentTranslationLanguageList?.length} languages`,
      image: Document,
      translationType: "document",
    },
    {
      id: "audio",
      title: "Translate audio",
      des: `${audioTranslationLanguageList?.length} languages`,
      image: Audio,
      translationType: "audioVideo",
    },
    {
      id: "history",
      title: "History",
      des: "Translation history",
      image: History,
      translationType: null, // Always available
    },
  ];

  // Filter cards based on subscription translation types
  const cardData = getAllCardData().filter((card) => {
    // History card is always available
    if (card.id === "history") {
      return true;
    }
    // Check if the translation type is available in subscription
    return isTranslationTypeAvailable(card.translationType);
  });

  // Function to reset translation state
  const resetTranslationState = () => {
    setTranslateText({
      source: "",
      target: "",
      text: "",
      translatedText: "",
    });
    setIsButtonDisabled(true);
    setIsSourceAutoDetected(false);
    setTranslatedDocuments([]);
    setTranslationStatus(null);
    setIsTranslationLoading(false);
    setResetKey((prev) => prev + 1);
  };

  // text translation api call
  const translateTextHandler = async () => {
    setIsTranslationLoading(true);
    try {
      const payload = {
        text: translateText.text,
        source_language: translateText.source?.code_alpha_1,
        target_language: translateText.target.code_alpha_1,
      };
      const response = await TranslateServices.translateTextService(payload);
      const { data, success } = response;
      if (success === 1) {
        let newSource = translateText.source;
        if (!translateText.source && data.source_language) {
          const detectedLang = languageData.find(
            (lang) => lang.code_alpha_1 === data.source_language
          );
          if (detectedLang) {
            newSource = detectedLang;
            setIsSourceAutoDetected(true);
          }
        }
        setTranslateText({
          ...translateText,
          source: newSource,
          target: translateText.target,
          translatedText: data.translatedText,
        });
        if (currentSubscription && account) {
          // Optimistically update UI
          const charsJustTranslated = translateText.text.replaceAll(" ", "").length;
          const newTranslatedCount = checkValidCount(translatedCount) + charsJustTranslated;
          setTranslatedCount(newTranslatedCount);
          const total = currentSubscription?.Subscription?.SubscriptionFeature?.translation || 0;
          setRemainingCount(Math.max(0, checkValidCount(total) - newTranslatedCount));

          // After a short delay, sync with backend
          setTimeout(() => {
            if (account) {
              const isCorporate = getIscorporateActive(account);
              const activeAcc = getActiveAccount(account);
              let bodyData = {
                order_id: currentSubscription?.order_id,
                total_count: data.total_char_count,
                is_corporate: isCorporate ? 1 : 0,
                corporate_id: activeAcc?.id,
              };
              TranslateServices.getTranslateRemainingCountService(bodyData)
                .then((res) => {
                  const { success1, data1 } = res;
                  if (success1 === 1) {
                    setTranslatedCount(data1?.translation);
                    setRemainingCount(Math.max(0, checkValidCount(total) - checkValidCount(data1?.translation)));
                  }
                })
                .catch((error) => {
                  console.log("error", error);
                });
            }
          }, 1500); // 1.5 seconds delay
        }
        setIsTranslationLoading(false);
      }
    } catch (error) {
      setIsTranslationLoading(false);
    } finally {
      setIsTranslationLoading(false);
    }
  };

  // get translated document api call
  const getTranslatedDocument = useCallback(async (documentData) => {
    try {
      let queryParams = {
        jobId: documentData.batchId,
      };
      const response = await TranslateServices.getTranslatedDocumentService({
        queryParams,
      });
      return response; // Return response for polling logic
    } catch (error) {
      return null;
    }
  }, []);


  // document translation api call
  const handleDocumentTranslation = async (files, langCode) => {
    let pollInterval = null;
    try {
      setIsTranslationLoading(true);
      setTranslationStatus("processing");

      const formData = new FormData();

      files.forEach((file) => {
        formData.append("files", file);
      });

      if (langCode) {
        formData.append("targetLanguage", langCode);
      }

      const response = await TranslateServices.multiDocumentTranslationService(
        formData
      );

      if (response.success === 1) {
        modalNotification({
          type: "success",
          message: "Document translation started successfully",
        });

        // Polling function
        const pollTranslatedDocument = async () => {
          const result = await getTranslatedDocument(response.data);
          if (result.data?.summary?.inProgress > 0) {
            if(result.data?.documents){
              setTranslatedDocuments(result.data?.documents);
            }
            // Keep polling
            pollInterval = setTimeout(pollTranslatedDocument, 10000); // 10 seconds interval
          } else {
            setTranslatedDocuments(result.data?.documents);
            // Translation complete, clear loading state
            setIsTranslationLoading(false);
            setTranslationStatus("completed");
            
            // Update character counts when translation is completed
            if (result.data?.summary?.totalCharacterCharged && currentSubscription && account) {
              const charsJustTranslated = result.data.summary.totalCharacterCharged;
              
              // Optimistically update UI
              const newTranslatedCount = checkValidCount(translatedCount) + charsJustTranslated;
              setTranslatedCount(newTranslatedCount);
              const total = currentSubscription?.Subscription?.SubscriptionFeature?.translation || 0;
              setRemainingCount(Math.max(0, checkValidCount(total) - newTranslatedCount));

              // After a short delay, sync with backend
              setTimeout(() => {
                if (account) {
                  const isCorporate = getIscorporateActive(account);
                  const activeAcc = getActiveAccount(account);
                  let bodyData = {
                    order_id: currentSubscription?.order_id,
                    total_count: charsJustTranslated,
                    is_corporate: isCorporate ? 1 : 0,
                    corporate_id: activeAcc?.id,
                  };
                  TranslateServices.getTranslateRemainingCountService(bodyData)
                    .then((res) => {
                      const { success, data } = res;
                      if (success === 1) {
                        setTranslatedCount(data?.translation);
                        const totalChars = currentSubscription?.Subscription?.SubscriptionFeature?.translation || 0;
                        setRemainingCount(Math.max(0, checkValidCount(totalChars) - checkValidCount(data?.translation)));
                      }
                    })
                    .catch((error) => {
                      console.log("error syncing document translation count", error);
                    });
                }
              }, 1500); // 1.5 seconds delay
            }
            
            if (pollInterval) clearTimeout(pollInterval);
          }
        };

        // Start polling after 10 seconds
        pollInterval = setTimeout(pollTranslatedDocument, 10000);

        // Optionally: return a cleanup function if needed
        return () => {
          if (pollInterval) clearTimeout(pollInterval);
        };
      } else {
        setTranslationStatus("failed");
        setIsTranslationLoading(false);
        modalNotification({
          type: "error",
          message: response.message || "Document translation failed",
        });
      }

      return response;
    } catch (error) {
      setTranslationStatus("failed");
      setIsTranslationLoading(false);
      modalNotification({
        type: "error",
        message:
          error.response?.data?.message ||
          error.message ||
          "Something went wrong with document translation",
      });
      throw error;
    }
  };

  // Reset translateText when selectedCard changes
  useEffect(() => {
    resetTranslationState();
  }, [selectedCard]);

  // Auto-select first available card if current selection is not available
  useEffect(() => {
    if (cardData.length > 1) {
      const isCurrentCardAvailable = cardData.some(card => card.id === selectedCard);
      if (!isCurrentCardAvailable) {
        // Prefer "text" if available, otherwise first card
        const textCard = cardData.find(card => card.id === "text");
        setSelectedCard(textCard ? textCard.id : cardData[0].id);
      }
    }
  }, [cardData, selectedCard]);

  // get language list and document translation language list
  useEffect(() => {
    dispatch(getLanguageList());
    getDocumentTranslationLanguageList();
    getAudioLanguageList();
  }, []);

  return (
    <>
      <div className="translate">
        <TranslateHeader />
        <div className="translate-cards">
          {cardData.map((card) => (
            <TransalteCard
              key={card.id}
              title={card.title}
              des={card.des}
              image={card.image}
              isActive={selectedCard === card.id}
              onClick={() => {
                if (selectedCard !== card.id) {
                  resetTranslationState();
                  setSelectedCard(card.id);
                }
              }}
            />
          ))}
        </div>

        {selectedCard === "audio" ? (
          <TranslateAudio 
            languageList={audioTranslationLanguageList} 
            currentSubscription={currentSubscription}
          />
        ) : selectedCard === "history" ? (
          <TranslateHistory
            documentTranslationLanguageList={documentTranslationLanguageList}
            getTranslatedDocument={getTranslatedDocument}
          />
        ) : (
          <TranslateBody
            resetKey={resetKey}
            type={selectedCard}
            isButtonDisabled={isButtonDisabled}
            setIsButtonDisabled={setIsButtonDisabled}
            translateText={translateText}
            setTranslateText={setTranslateText}
            sourceLangDropdown={fromLangDropdown}
            setSourceLangDropdown={setFromLangDropdown}
            targetLangDropdown={toLangDropdown}
            setTargetLangDropdown={setToLangDropdown}
            sourceLanguageDropdownOpen={sourceLanguageDropdownOpen}
            sourceLanguageDropdownClose={sourceLanguageDropdownClose}
            targetLanguageDropdownOpen={targetLanguageDropdownOpen}
            targetLanguageDropdownClose={targetLanguageDropdownClose}
            languageData={languageData}
            documentTranslationLanguageList={documentTranslationLanguageList}
            search={search}
            setSearch={setSearch}
            translateTextHandler={translateTextHandler}
            handleDocumentTranslation={handleDocumentTranslation}
            isTranslationLoading={isTranslationLoading}
            translationStatus={translationStatus}
            remainingCount={remainingCount}
            errorMsgCount0={errorMsgCount0}
            translatedDocuments={translatedDocuments}
            setTranslatedDocuments={setTranslatedDocuments}
            isSourceAutoDetected={isSourceAutoDetected}
            currentSubscription={currentSubscription}
          />
        )}
      </div>
      <div className="character-count">
        <div className="character-count-box">
          <IoLanguageOutline />
          <div>
            {translatedCount} <span>Characters Translated</span>
          </div>
        </div>
        <div className="character-count-box">
          <IoLanguageOutline />
          <div>
            {remainingCount} <span>Characters Remaining</span>
          </div>
        </div>
      </div>
    </>
  );
}

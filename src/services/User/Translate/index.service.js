import { Translate } from "../../../apiEndPoints";
import config from "../../../config";
import { logger } from "../../../utils";
import APIrequest, { AppAPIRequest } from "../../axios";

const translationBaseUrl = config.TRANSLATION_URL;
const appBaseUrl = config.STAG_BASE_URL;
const baseUrl = config.BASE_URL;
// const translationToken = config.TRANSLATION_KEY;
export const TranslateServices = {
  /**
   *
   * @returns
   * Function To handle Login action
   */

  languageListingService: async ({ queryParams }) => {
    try {
      const payload = {
        ...Translate.languageListing,
        queryParams,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  translateTextService: async (bodyData) => {
    try {
      const payload = {
        ...Translate.translateText,
        bodyData,
        baseURL: baseUrl,
        // token: translationToken,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  translateHtmlService: async (bodyData) => {
    try {
      const payload = {
        ...Translate.translateHtml,
        bodyData,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  translateDocumentService: async (bodyData, formHeaders) => {
    try {
      const payload = {
        ...Translate.translateDocument,
        bodyData,
        formHeaders,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  translateMediaService: async (bodyData, formHeaders) => {
    try {
      const payload = {
        ...Translate.translateSpeechDocument,
        bodyData,
        formHeaders,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getTranslateDocumentService: async ({ queryParams }) => {
    try {
      const payload = {
        ...Translate.getEncodedDocument,
        queryParams,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getSpeechLanguageService: async () => {
    try {
      const payload = {
        ...Translate.translateSpeechLanguageList,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  translateSpeechToTextService: async (bodyData, formHeaders) => {
    try {
      const payload = {
        ...Translate.translateSpeechToText,
        bodyData,
        formHeaders,
        baseURL: translationBaseUrl,
        // token: translationToken,
        token: null,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getTranslateRemainingCountService: async (bodyData) => {
    try {
      const payload = {
        ...Translate.translateCount,
        bodyData,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  createAudioTranslationJobService: async (bodyData, headers) => {
    try {
      const payload = {
        ...Translate.createJob,
        bodyData,
        baseURL: appBaseUrl,
        formHeaders: headers,
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getLanguageListService: async () => {
    try {
      const payload = {
        ...Translate.getLanguageList,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getJobsListService: async (pageNo) => {
    try {
      const payload = {
        ...Translate.getJobsList,
        queryParams: pageNo,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getJobStatusService: async (jobId) => {
    try {
      const payload = {
        url: `${Translate.getJobStatus.url}/${jobId}`,
        method: Translate.getJobStatus.method,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  documentTranslationLanguageListService: async () => {
    try {
      const payload = {
        ...Translate.documentTranslationLanguageList,
        baseURL: appBaseUrl,
      };
      const res = await AppAPIRequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  singleDocumentTranslationService: async (bodyData, formHeaders) => {
    try {
      const payload = {
        ...Translate.singleDocumentTranslation,
        bodyData,
        formHeaders,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  multiDocumentTranslationService: async (bodyData) => {
    try {
      const payload = {
        ...Translate.multiDocumentTranslation,
        bodyData,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getTranslatedDocumentService: async ({ queryParams }) => {
    try {
      const payload = {
        url: `${Translate.getTranslatedDocument.url}/${queryParams.jobId}`,
        method: Translate.getTranslatedDocument.method,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  getHistoryService: async () => {
    try {
      const payload = {
        ...Translate.history,
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  deleteDocumentService: async (ids) => {
    try {
      const payload = {
        ...Translate.deleteDocument,
        bodyData: {
          ids
        },
        baseURL: appBaseUrl,
      };
      console.log("payload",payload);
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
  searchDocumentService: async (query) => {
    try {
      const payload = {
        ...Translate.searchDocument,
        bodyData: { query },
        baseURL: appBaseUrl,
      };
      const res = await APIrequest(payload);
      return res;
    } catch (error) {
      logger(error);
      throw error;
    }
  },
};
